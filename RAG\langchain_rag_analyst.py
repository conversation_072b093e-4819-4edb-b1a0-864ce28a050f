# -*- coding: utf-8 -*-
# Updated RAG pipeline with LCEL, batching, MMR retriever, and light error handling

from dotenv import load_dotenv

load_dotenv()

from typing import List
import os

# Loaders & core LC
from ..doc_loader import load_docs
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_community.vectorstores import FAISS
from langchain_openai import OpenAIEmbeddings, ChatOpenAI

# LCEL / Core
from langchain_core.prompts import PromptTemplate
from langchain_core.runnables import RunnablePassthrough, RunnableLambda
from langchain_core.output_parsers import StrOutputParser

# ---------- 1) Load documents ----------
# docs = load_docs(["data/dji_store.text"])
docs = load_docs(["data/dji_store.xlsx"])

# ---------- 2) Split ----------
splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=100)
chunks = splitter.split_documents(docs)

# ---------- 3) Summarize each chunk (BATCH) ----------
llm = ChatOpenAI(model="gpt-5-mini", temperature=0)

summary_prompt = PromptTemplate.from_template(
    "สรุปย่อเนื้อหาต่อไปนี้เป็นภาษาไทยแบบกระชับ 1-2 บรรทัด "
    "ห้ามเพิ่ม/เดาข้อมูลใหม่ และคงตัวเลข/ชื่อเฉพาะที่สำคัญ:\n\n{text}"
)
summary_chain = summary_prompt | llm | StrOutputParser()

# Batch summarize (faster than loop)
if chunks:
    inputs = [{"text": d.page_content} for d in chunks]
    summaries = summary_chain.batch(inputs, {"max_concurrency": 8})
    for d, s in zip(chunks, summaries):
        d.metadata["summary"] = s
else:
    summaries = []

print(f"Loaded {len(docs)} docs -> {len(chunks)} chunks, {len(summaries)} summaries")

# # ---------- 4) Vector Index ----------
embeddings = OpenAIEmbeddings(model="text-embedding-3-large")
vectorstore = FAISS.from_documents(chunks, embeddings)
# vectorstore.save_local("data/dji_store_vectorstore")
# vectorstore = FAISS.load_local(
#     "data/dji_store_vectorstore",
#     embeddings,
#     allow_dangerous_deserialization=True  # needed if you trust your own pickle
# )

# ---------- 5) Retriever (use MMR to reduce redundancy) ----------
retriever = vectorstore.as_retriever(
    search_type="mmr", search_kwargs={"k": 6, "fetch_k": 20, "lambda_mult": 0.3}
)


# ---------- 6) Context Builder ----------
def build_context_fn(docs: List):
    print("\n\n----- build_context_fn -----")
    print(f"build_context_fn: {len(docs)} docs")
    print(docs)
    context = "## สรุประดับเอกสาร\n"
    summaries = [d.metadata.get("summary", "") for d in docs]
    context += "\n".join(summaries)
    context += "\n\n## ส่วนที่เกี่ยวข้อง\n"
    for i, d in enumerate(docs):
        context += f"\n### Section {i} [#{i}]\n{d.page_content}\n"
    
    print("\n\n----- context -----")
    print(context)
    return context


build_context = RunnableLambda(build_context_fn)

# ---------- 7) Answer Chain (LCEL) ----------
answer_prompt = PromptTemplate.from_template(
    """
คุณคือผู้ช่วยตอบคำถามสำหรับร้านค้า  

- หากคำถามเป็นเพียงการทักทาย เช่น "สวัสดี", "hello", "hi"  
  ให้ตอบกลับด้วยคำทักทายที่สุภาพและเชิญชวนให้ถามข้อมูลสินค้า/บริการ เช่น:  
  "สวัสดีค่ะ ยินดีต้อนรับ หากสนใจสอบถามข้อมูลสินค้าและบริการ สามารถถามได้เลยนะคะ"  

- หากคำถามไม่เกี่ยวข้องกับสินค้า บริการ หรือร้านค้า ให้ตอบว่า:  
  "คำถามนี้ไม่เกี่ยวข้องกับร้านค้า หากมีคำถามเกี่ยวกับสินค้าและบริการ สามารถสอบถามเพิ่มเติมได้เลยค่ะ"  

- หากไม่พบคำตอบจากข้อมูลในเอกสาร ให้ตอบกลับว่า:  
  "กรุณาติดต่อเจ้าหน้าที่เกี่ยวกับ [ประเด็นคำถาม] [ช่องทางการติดต่อ]"  
  (แนะนำช่องทางการติดต่อเฉพาะกรณีนี้เท่านั้น)  e

- หากคำถามเกี่ยวข้องกับการเปรียบเทียบสินค้า ให้สร้างตารางสั้นๆ เพื่อเปรียบเทียบข้อดีและข้อเสียของสินค้าแต่ละตัว  
  (ไม่ต้องแนะนำช่องทางการติดต่อ)  

คำถาม: {question}  
บริบท: {context}

"""
)

answer_chain = answer_prompt | llm | StrOutputParser()

# Compose QA chain with LCEL:
# maps the user query -> {"question": query, "context": (retriever -> context string)}
qa_chain = {
    "question": RunnablePassthrough(),
    "context": retriever | build_context,
} | answer_chain


# ---------- Convenience function ----------
def ask(query: str) -> str:
    try:
        return qa_chain.invoke(query)
    except Exception as e:
        return f"[ERROR] {e}"


# ---------- Examples ----------
if __name__ == "__main__":
    # print(ask("สวัสดีครับ"))
    # print(ask("ดีจ๊ะ กินข้าวยัง"))
    # print(ask("คืนสินค้าได้ไหม"))
    print(ask("มี DJI Mavic 3 Pro ไหม"))
    # print(ask("เปรียบเทียบ DJI Mavic 3 Pro กับ DJI Avata 2"))
    # print(ask("DJI Mini 3 Pro"))
    # print(ask("เปรียบเทียบ DJI Mavic 3 Pro กับ DJI Air 2S"))
